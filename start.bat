@echo off
chcp 65001 >nul

echo 🚀 启动 AutoGen Chat Application...

REM 检查是否在正确的目录
if not exist "backend" (
    echo ❌ 错误: 请在项目根目录运行此脚本
    pause
    exit /b 1
)

if not exist "frontend" (
    echo ❌ 错误: 请在项目根目录运行此脚本
    pause
    exit /b 1
)

REM 启动后端服务
echo 📡 启动后端服务...
cd backend
start "Backend Server" cmd /k "python main.py"
cd ..

REM 等待后端启动
echo ⏳ 等待后端服务启动...
timeout /t 5 /nobreak >nul

REM 启动前端服务
echo 🎨 启动前端服务...
cd frontend
start "Frontend Server" cmd /k "npm run dev"
cd ..

echo 🎉 应用启动完成!
echo 📱 前端地址: http://localhost:5173
echo 🔧 后端地址: http://localhost:8000
echo.
echo 请在浏览器中访问前端地址开始使用
pause
