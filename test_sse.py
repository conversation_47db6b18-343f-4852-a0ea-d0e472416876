#!/usr/bin/env python3
"""
测试SSE格式的简单脚本
"""

import asyncio
import json
from backend.autogen_service import autogen_service

async def test_sse():
    print("Testing SSE format...")
    
    async for chunk in autogen_service.chat_stream("Hello", "test_conv"):
        print(f"Raw chunk: {repr(chunk)}")
        if chunk.count("data:") > 1:
            print("❌ Found duplicate 'data:' prefix!")
        else:
            print("✅ Chunk format looks good")
        break  # Just test the first chunk

if __name__ == "__main__":
    asyncio.run(test_sse())
