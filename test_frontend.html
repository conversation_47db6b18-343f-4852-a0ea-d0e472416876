<!DOCTYPE html>
<html>
<head>
    <title>Test SSE Frontend</title>
</head>
<body>
    <h1>Test SSE Connection</h1>
    <div id="messages"></div>
    <button onclick="testSSE()">Test SSE</button>
    
    <script>
        function testSSE() {
            const messagesDiv = document.getElementById('messages');
            messagesDiv.innerHTML = '<p>Starting SSE test...</p>';
            
            fetch('http://localhost:8000/api/chat/stream', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    message: 'Hello test',
                    conversation_id: 'test_frontend'
                })
            })
            .then(response => {
                const reader = response.body.getReader();
                const decoder = new TextDecoder();
                let buffer = '';
                
                function readStream() {
                    reader.read().then(({ done, value }) => {
                        if (done) {
                            messagesDiv.innerHTML += '<p>✅ Stream completed</p>';
                            return;
                        }
                        
                        buffer += decoder.decode(value, { stream: true });
                        const lines = buffer.split('\n');
                        buffer = lines.pop() || '';
                        
                        for (const line of lines) {
                            if (line.startsWith('data: ')) {
                                const data = line.slice(6);
                                
                                if (data === '[DONE]') {
                                    messagesDiv.innerHTML += '<p>🏁 Received [DONE]</p>';
                                    return;
                                }
                                
                                try {
                                    const parsed = JSON.parse(data);
                                    messagesDiv.innerHTML += `<p>📨 ${parsed.content}</p>`;
                                } catch (e) {
                                    messagesDiv.innerHTML += `<p>❌ Parse error: ${data}</p>`;
                                }
                            }
                        }
                        
                        readStream();
                    });
                }
                
                readStream();
            })
            .catch(error => {
                messagesDiv.innerHTML += `<p>❌ Error: ${error}</p>`;
            });
        }
    </script>
</body>
</html>
