from pydantic import BaseModel
from typing import Optional, List, Dict, Any


class ChatMessage(BaseModel):
    role: str  # "user" or "assistant"
    content: str
    timestamp: Optional[str] = None


class ChatRequest(BaseModel):
    message: str
    conversation_id: Optional[str] = None
    model: Optional[str] = "gpt-4"


class ChatResponse(BaseModel):
    message: str
    conversation_id: str
    status: str = "success"


class StreamResponse(BaseModel):
    content: str
    done: bool = False
    conversation_id: Optional[str] = None
