import asyncio
import json
from typing import AsyncGenerator, Dict, Any, Optional
from autogen import ConversableAgent, LLMConfig
import os
from dotenv import load_dotenv

load_dotenv()


class AutoGenService:
    def __init__(self):
        self.conversations: Dict[str, Dict[str, Any]] = {}
        self.setup_agents()

    def setup_agents(self):
        """设置AutoGen代理"""
        # 配置LLM
        llm_config = LLMConfig(
            model="gpt-4",
            api_key=os.getenv("OPENAI_API_KEY", "your-api-key-here"),
            base_url=os.getenv("OPENAI_BASE_URL", "https://api.openai.com/v1"),
            temperature=0.7,
        )

        # 创建用户代理
        self.user_proxy = ConversableAgent(
            name="user_proxy",
            system_message="You are a helpful assistant.",
            llm_config=llm_config,
            human_input_mode="NEVER",
            max_consecutive_auto_reply=1,
        )

        # 创建助手代理
        self.assistant = ConversableAgent(
            name="assistant",
            system_message="""You are a helpful AI assistant.
            Provide clear, accurate, and helpful responses to user questions.
            Be conversational and engaging while maintaining professionalism.""",
            llm_config=llm_config,
            human_input_mode="NEVER",
        )
    
    async def chat_stream(self, message: str, conversation_id: str) -> AsyncGenerator[str, None]:
        """流式聊天响应"""
        try:
            # 初始化对话历史
            if conversation_id not in self.conversations:
                self.conversations[conversation_id] = {
                    "messages": [],
                    "context": {}
                }

            # 添加用户消息到历史
            self.conversations[conversation_id]["messages"].append({
                "role": "user",
                "content": message
            })

            # 检查是否有有效的API密钥
            api_key = os.getenv("OPENAI_API_KEY", "your-api-key-here")
            if api_key == "your-api-key-here" or not api_key.startswith("sk-"):
                # 使用演示模式
                response_content = self._get_demo_response(message)
            else:
                # 使用真实的AutoGen
                try:
                    chat_result = self.user_proxy.initiate_chat(
                        self.assistant,
                        message=message,
                        max_turns=1
                    )

                    # 获取助手的回复
                    response_content = "I'm here to help! How can I assist you today?"
                    if chat_result and hasattr(chat_result, 'chat_history'):
                        for msg in chat_result.chat_history:
                            if msg.get('name') == 'assistant':
                                response_content = msg.get('content', '')
                                break
                except Exception as e:
                    response_content = f"AutoGen Error: {str(e)}"

            # 模拟流式输出
            words = response_content.split()
            current_response = ""

            for i, word in enumerate(words):
                current_response += word + " "
                yield json.dumps({
                    "content": current_response.strip(),
                    "done": False,
                    "conversation_id": conversation_id
                })
                await asyncio.sleep(0.05)  # 模拟打字效果

            # 发送完成信号
            yield json.dumps({
                "content": current_response.strip(),
                "done": True,
                "conversation_id": conversation_id
            })

            # 添加助手回复到历史
            self.conversations[conversation_id]["messages"].append({
                "role": "assistant",
                "content": current_response.strip()
            })

        except Exception as e:
            error_msg = f"Error in chat processing: {str(e)}"
            yield json.dumps({
                "content": error_msg,
                "done": True,
                "conversation_id": conversation_id,
                "error": True
            })

    def _get_demo_response(self, message: str) -> str:
        """演示模式的响应生成器"""
        message_lower = message.lower()

        # 基于关键词的简单响应
        if any(word in message_lower for word in ['hello', 'hi', 'hey', '你好']):
            return "Hello! I'm AutoGen Assistant, powered by AI agents. I'm here to help you with various tasks. What would you like to know or discuss today?"

        elif any(word in message_lower for word in ['python', 'code', 'programming', '编程']):
            return """I'd be happy to help with Python programming! Here's a simple example:

```python
def greet(name):
    return f"Hello, {name}! Welcome to AutoGen Chat!"

# Usage
message = greet("Developer")
print(message)
```

This function demonstrates basic Python syntax. What specific programming topic would you like to explore?"""

        elif any(word in message_lower for word in ['machine learning', 'ml', 'ai', '机器学习']):
            return """Machine Learning is a fascinating field! Here are the key concepts:

## Types of Machine Learning:
1. **Supervised Learning** - Learning with labeled data
2. **Unsupervised Learning** - Finding patterns in unlabeled data
3. **Reinforcement Learning** - Learning through interaction and rewards

## Popular Algorithms:
- Linear Regression
- Decision Trees
- Neural Networks
- Support Vector Machines

Would you like me to explain any of these concepts in more detail?"""

        elif any(word in message_lower for word in ['joke', 'funny', '笑话']):
            return """Here's a programming joke for you:

Why do programmers prefer dark mode?

Because light attracts bugs! 🐛💡

😄 Hope that brought a smile to your face! Do you have any technical questions I can help with?"""

        elif any(word in message_lower for word in ['help', 'what can you do', '帮助']):
            return """I'm AutoGen Assistant, and I can help you with:

🔧 **Programming & Development**
- Python, JavaScript, and other languages
- Code examples and debugging
- Best practices and architecture

📚 **Learning & Education**
- Explaining complex concepts
- Step-by-step tutorials
- Q&A on various topics

💡 **Problem Solving**
- Breaking down complex problems
- Suggesting solutions and approaches
- Brainstorming ideas

🤖 **AI & Technology**
- Machine learning concepts
- Latest tech trends
- Tool recommendations

What would you like to explore today?"""

        else:
            return f"""Thank you for your message: "{message}"

I'm AutoGen Assistant, running in demo mode. While I don't have access to real AI models right now, I can still help with various topics including:

- Programming and development
- Technology explanations
- General questions and discussions

To enable full AI capabilities, please configure a valid OpenAI API key in the backend/.env file.

How else can I assist you today?"""
    
    def get_conversation_history(self, conversation_id: str) -> list:
        """获取对话历史"""
        if conversation_id in self.conversations:
            return self.conversations[conversation_id]["messages"]
        return []


# 全局服务实例
autogen_service = AutoGenService()
