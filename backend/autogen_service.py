import asyncio
import json
from typing import AsyncGenerator, Dict, Any, Optional
from autogen import ConversableAgent, LLMConfig
import os
from dotenv import load_dotenv

load_dotenv()


class AutoGenService:
    def __init__(self):
        self.conversations: Dict[str, Dict[str, Any]] = {}
        self.setup_agents()

    def setup_agents(self):
        """设置AutoGen代理"""
        # 配置LLM
        llm_config = LLMConfig(
            model="gpt-4",
            api_key=os.getenv("OPENAI_API_KEY", "your-api-key-here"),
            base_url=os.getenv("OPENAI_BASE_URL", "https://api.openai.com/v1"),
            temperature=0.7,
        )

        # 创建用户代理
        self.user_proxy = ConversableAgent(
            name="user_proxy",
            system_message="You are a helpful assistant.",
            llm_config=llm_config,
            human_input_mode="NEVER",
            max_consecutive_auto_reply=1,
        )

        # 创建助手代理
        self.assistant = ConversableAgent(
            name="assistant",
            system_message="""You are a helpful AI assistant.
            Provide clear, accurate, and helpful responses to user questions.
            Be conversational and engaging while maintaining professionalism.""",
            llm_config=llm_config,
            human_input_mode="NEVER",
        )
    
    async def chat_stream(self, message: str, conversation_id: str) -> AsyncGenerator[str, None]:
        """流式聊天响应"""
        try:
            # 初始化对话历史
            if conversation_id not in self.conversations:
                self.conversations[conversation_id] = {
                    "messages": [],
                    "context": {}
                }
            
            # 添加用户消息到历史
            self.conversations[conversation_id]["messages"].append({
                "role": "user",
                "content": message
            })
            
            # 使用AutoGen进行对话
            response_content = ""
            
            # 模拟流式响应 - 在实际实现中，您需要根据AutoGen的具体API进行调整
            chat_result = self.user_proxy.initiate_chat(
                self.assistant,
                message=message,
                max_turns=1
            )
            
            # 获取助手的回复
            if chat_result and hasattr(chat_result, 'chat_history'):
                for msg in chat_result.chat_history:
                    if msg.get('name') == 'assistant':
                        response_content = msg.get('content', '')
                        break
            else:
                # 备用响应获取方法
                response_content = "I'm here to help! How can I assist you today?"
            
            # 模拟流式输出
            words = response_content.split()
            current_response = ""
            
            for i, word in enumerate(words):
                current_response += word + " "
                yield json.dumps({
                    "content": current_response.strip(),
                    "done": False,
                    "conversation_id": conversation_id
                })
                await asyncio.sleep(0.05)  # 模拟打字效果
            
            # 发送完成信号
            yield json.dumps({
                "content": current_response.strip(),
                "done": True,
                "conversation_id": conversation_id
            })
            
            # 添加助手回复到历史
            self.conversations[conversation_id]["messages"].append({
                "role": "assistant",
                "content": current_response.strip()
            })
            
        except Exception as e:
            error_msg = f"Error in chat processing: {str(e)}"
            yield json.dumps({
                "content": error_msg,
                "done": True,
                "conversation_id": conversation_id,
                "error": True
            })
    
    def get_conversation_history(self, conversation_id: str) -> list:
        """获取对话历史"""
        if conversation_id in self.conversations:
            return self.conversations[conversation_id]["messages"]
        return []


# 全局服务实例
autogen_service = AutoGenService()
