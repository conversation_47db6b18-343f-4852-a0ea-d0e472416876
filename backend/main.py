from fastapi import Fast<PERSON><PERSON>, HTTPException
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import StreamingResponse
from sse_starlette.sse import EventSourceResponse
import uuid
import json
from models import ChatRequest, ChatResponse, StreamResponse
from autogen_service import autogen_service

app = FastAPI(title="AutoGen Chat API", version="1.0.0")

# 配置CORS
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # 在生产环境中应该设置具体的域名
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)


@app.get("/")
async def root():
    return {"message": "AutoGen Chat API is running!"}


@app.get("/health")
async def health_check():
    return {"status": "healthy", "service": "autogen-chat-api"}


@app.post("/api/chat")
async def chat(request: ChatRequest):
    """非流式聊天接口"""
    try:
        conversation_id = request.conversation_id or str(uuid.uuid4())
        
        # 获取完整响应
        full_response = ""
        async for chunk in autogen_service.chat_stream(request.message, conversation_id):
            data = json.loads(chunk)
            if data.get("done"):
                full_response = data.get("content", "")
                break
        
        return ChatResponse(
            message=full_response,
            conversation_id=conversation_id,
            status="success"
        )
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


@app.post("/api/chat/stream")
async def chat_stream(request: ChatRequest):
    """流式聊天接口"""
    try:
        conversation_id = request.conversation_id or str(uuid.uuid4())
        
        async def generate():
            async for chunk in autogen_service.chat_stream(request.message, conversation_id):
                yield f"data: {chunk}\n\n"
            yield "data: [DONE]\n\n"
        
        return EventSourceResponse(
            generate(),
            media_type="text/plain",
            headers={
                "Cache-Control": "no-cache",
                "Connection": "keep-alive",
                "Access-Control-Allow-Origin": "*",
                "Access-Control-Allow-Headers": "*",
            }
        )
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


@app.get("/api/conversation/{conversation_id}/history")
async def get_conversation_history(conversation_id: str):
    """获取对话历史"""
    try:
        history = autogen_service.get_conversation_history(conversation_id)
        return {"conversation_id": conversation_id, "messages": history}
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


if __name__ == "__main__":
    import uvicorn
    uvicorn.run(
        "main:app",
        host="0.0.0.0",
        port=8000,
        reload=True,
        log_level="info"
    )
