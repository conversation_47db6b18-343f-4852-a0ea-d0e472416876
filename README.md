# AutoGen Chat Application

一个基于AutoGen 0.5.7和FastAPI的炫酷聊天应用，具有Gemini风格的前端界面。

## 项目结构

```
.
├── backend/                 # 后端代码
│   ├── main.py             # FastAPI主应用
│   ├── models.py           # 数据模型
│   ├── autogen_service.py  # AutoGen服务封装
│   ├── requirements.txt    # Python依赖
│   └── .env               # 环境配置
├── frontend/               # 前端代码
│   ├── src/
│   │   ├── components/    # React组件
│   │   ├── services/      # API服务
│   │   └── App.tsx        # 主应用组件
│   ├── package.json       # Node.js依赖
│   └── ...
└── README.md              # 项目说明
```

## 功能特性

### 后端功能
- ✅ FastAPI框架，提供高性能API服务
- ✅ SSE (Server-Sent Events) 流式输出
- ✅ AutoGen 0.5.7集成，支持多代理对话
- ✅ CORS配置，支持跨域请求
- ✅ 环境变量配置管理
- ✅ 对话历史管理

### 前端功能
- ✅ React + TypeScript + Vite现代化开发栈
- ✅ Ant Design X组件库，炫酷UI设计
- ✅ Gemini风格界面，渐变背景和动画效果
- ✅ 实时流式消息显示
- ✅ Markdown渲染支持
- ✅ 代码高亮显示
- ✅ 响应式设计，支持移动端
- ✅ 打字动画效果

## 快速开始

### 一键启动（推荐）

**Linux/macOS:**
```bash
./start.sh
```

**Windows:**
```batch
start.bat
```

### 手动安装和运行

#### 后端安装

1. 进入后端目录：
   ```bash
   cd backend
   ```

2. 安装依赖：
   ```bash
   pip install -r requirements.txt
   ```

3. 配置环境变量：
   编辑 `.env` 文件，设置你的OpenAI API密钥：
   ```
   OPENAI_API_KEY=your-openai-api-key-here
   ```

4. 启动后端服务：
   ```bash
   python main.py
   ```

   后端服务将在 `http://localhost:8000` 启动

#### 前端安装

1. 进入前端目录：
   ```bash
   cd frontend
   ```

2. 安装依赖：
   ```bash
   npm install
   ```

3. 启动前端服务：
   ```bash
   npm run dev
   ```

   前端服务将在 `http://localhost:5173` 启动

### 访问应用

启动成功后，在浏览器中访问 `http://localhost:5173` 即可使用聊天应用。

## API接口

### 流式聊天接口
- **POST** `/api/chat/stream`
- 支持SSE流式输出
- 请求体：
  ```json
  {
    "message": "用户消息",
    "conversation_id": "可选的对话ID",
    "model": "gpt-4"
  }
  ```

### 同步聊天接口
- **POST** `/api/chat`
- 返回完整响应
- 请求体格式同上

### 对话历史接口
- **GET** `/api/conversation/{conversation_id}/history`
- 获取指定对话的历史记录

### 健康检查接口
- **GET** `/health`
- 检查服务状态

## 技术栈

### 后端技术
- **FastAPI**: 现代化的Python Web框架
- **AutoGen 0.5.7**: 微软开源的多代理对话框架
- **SSE-Starlette**: 服务器发送事件支持
- **Pydantic**: 数据验证和序列化
- **Uvicorn**: ASGI服务器

### 前端技术
- **React 18**: 现代化的前端框架
- **TypeScript**: 类型安全的JavaScript
- **Vite**: 快速的构建工具
- **Ant Design X**: 企业级UI组件库
- **React Markdown**: Markdown渲染
- **React Syntax Highlighter**: 代码高亮

## 界面特色

- 🎨 **Gemini风格设计**: 参考Google Gemini的现代化界面
- 🌈 **渐变背景**: 动态渐变背景效果
- ✨ **流畅动画**: 消息滑入、打字效果等动画
- 🎯 **响应式布局**: 适配各种屏幕尺寸
- 🔥 **炫酷特效**: 毛玻璃效果、阴影、发光等视觉效果

## 开发说明

### 后端开发
- 所有API接口都有完整的类型注解
- 使用Pydantic进行数据验证
- 支持热重载开发模式
- 完整的错误处理和日志记录

### 前端开发
- 使用TypeScript确保类型安全
- 组件化开发，易于维护和扩展
- CSS-in-JS样式管理
- 支持热模块替换(HMR)

## 注意事项

1. 确保已安装AutoGen 0.5.7相关组件
2. 需要有效的OpenAI API密钥
3. 前后端需要同时运行才能正常使用
4. 建议使用现代浏览器以获得最佳体验

## 故障排除

### 常见问题

1. **后端启动失败**
   - 检查Python版本（建议3.8+）
   - 确认所有依赖已正确安装
   - 检查端口8000是否被占用

2. **前端无法连接后端**
   - 确认后端服务已启动
   - 检查CORS配置
   - 确认API地址配置正确

3. **AutoGen相关错误**
   - 确认OpenAI API密钥配置正确
   - 检查网络连接
   - 查看后端日志获取详细错误信息

## 许可证

MIT License
