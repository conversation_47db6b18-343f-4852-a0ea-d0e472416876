import React from 'react';
import { ConfigProvider, theme } from 'antd';
import ChatApp from './components/ChatApp';
import './App.css';

function App() {
  return (
    <ConfigProvider
      theme={{
        algorithm: theme.darkAlgorithm,
        token: {
          colorPrimary: '#4285f4',
          colorBgBase: '#0f0f0f',
          colorBgContainer: '#1a1a1a',
          colorBorder: '#333333',
          colorText: '#ffffff',
          colorTextSecondary: '#b3b3b3',
          borderRadius: 12,
        },
      }}
    >
      <div className="app">
        <ChatApp />
      </div>
    </ConfigProvider>
  );
}

export default App;
