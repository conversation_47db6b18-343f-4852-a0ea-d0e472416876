import React, { useState, useEffect, useRef } from 'react';
import { Button, Input, Card, Typography, Space, Avatar, Spin, Drawer } from 'antd';
import { 
  SendOutlined, 
  RobotOutlined, 
  UserOutlined, 
  PlusOutlined, 
  HistoryOutlined,
  MenuOutlined,
  CloseOutlined
} from '@ant-design/icons';
import ReactMarkdown from 'react-markdown';
import { Prism as SyntaxHighlighter } from 'react-syntax-highlighter';
import { vscDarkPlus } from 'react-syntax-highlighter/dist/esm/styles/prism';
import './ChatApp.css';
import './EnhancedChatApp.css';

const { Title, Text } = Typography;
const { TextArea } = Input;

// 消息接口
interface Message {
  id: string;
  role: 'user' | 'assistant';
  content: string;
  timestamp: Date;
}

// 对话接口
interface Conversation {
  id: string;
  title: string;
  lastMessage: string;
  timestamp: Date;
  messageCount: number;
}

const EnhancedChatApp: React.FC = () => {
  // 状态管理
  const [messages, setMessages] = useState<Message[]>([]);
  const [inputValue, setInputValue] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [currentConversationId, setCurrentConversationId] = useState<string>('');
  const [conversations, setConversations] = useState<Conversation[]>([]);
  const [sidebarVisible, setSidebarVisible] = useState(false);
  const [isMobile, setIsMobile] = useState(false);
  
  const messagesEndRef = useRef<HTMLDivElement>(null);

  // 检测移动设备
  useEffect(() => {
    const checkMobile = () => {
      setIsMobile(window.innerWidth <= 768);
    };
    
    checkMobile();
    window.addEventListener('resize', checkMobile);
    return () => window.removeEventListener('resize', checkMobile);
  }, []);

  // 初始化时创建新对话
  useEffect(() => {
    handleNewConversation();
    loadConversationHistory();
  }, []);

  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  };

  useEffect(() => {
    scrollToBottom();
  }, [messages]);

  // 加载对话历史列表
  const loadConversationHistory = async () => {
    try {
      const response = await fetch('http://localhost:8000/api/conversations');
      if (response.ok) {
        const data = await response.json();
        const conversations = data.conversations.map((conv: any) => ({
          ...conv,
          timestamp: new Date(conv.timestamp)
        }));
        setConversations(conversations);
      } else {
        // 如果API调用失败，使用模拟数据
        const mockConversations: Conversation[] = [
          {
            id: 'conv_1',
            title: 'Python基础语法讨论',
            lastMessage: '谢谢你的详细解释！',
            timestamp: new Date(Date.now() - 86400000),
            messageCount: 8
          },
          {
            id: 'conv_2',
            title: '机器学习入门',
            lastMessage: '请推荐一些学习资源',
            timestamp: new Date(Date.now() - 172800000),
            messageCount: 12
          }
        ];
        setConversations(mockConversations);
      }
    } catch (error) {
      console.error('Failed to load conversation history:', error);
      // 使用模拟数据作为后备
      setConversations([]);
    }
  };

  // 创建新对话
  const handleNewConversation = () => {
    const newConversationId = `conv_${Date.now()}`;
    setCurrentConversationId(newConversationId);
    setMessages([]);
    setInputValue('');
    
    // 在移动设备上自动关闭侧边栏
    if (isMobile) {
      setSidebarVisible(false);
    }
  };

  // 切换到指定对话
  const handleSwitchConversation = async (conversationId: string) => {
    try {
      setCurrentConversationId(conversationId);

      // 调用真实API获取对话历史
      const response = await fetch(`http://localhost:8000/api/conversation/${conversationId}/history`);
      if (response.ok) {
        const data = await response.json();
        const messages = data.messages.map((msg: any) => ({
          ...msg,
          timestamp: new Date(msg.timestamp || Date.now())
        }));
        setMessages(messages);
      } else {
        // 如果API调用失败，清空消息
        setMessages([]);
      }

      // 在移动设备上自动关闭侧边栏
      if (isMobile) {
        setSidebarVisible(false);
      }
    } catch (error) {
      console.error('Failed to load conversation:', error);
      setMessages([]);
    }
  };

  // 发送消息
  const handleSendMessage = async () => {
    if (!inputValue.trim() || isLoading) return;

    const userMessage: Message = {
      id: `msg_${Date.now()}`,
      role: 'user',
      content: inputValue.trim(),
      timestamp: new Date(),
    };

    setMessages(prev => [...prev, userMessage]);
    setInputValue('');
    setIsLoading(true);

    // 创建助手消息占位符
    const assistantMessageId = `msg_${Date.now() + 1}`;
    const assistantMessage: Message = {
      id: assistantMessageId,
      role: 'assistant',
      content: '',
      timestamp: new Date(),
    };

    setMessages(prev => [...prev, assistantMessage]);

    try {
      const response = await fetch('http://localhost:8000/api/chat/stream', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          message: userMessage.content,
          conversation_id: currentConversationId
        }),
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const reader = response.body?.getReader();
      if (!reader) {
        throw new Error('No response body reader available');
      }

      const decoder = new TextDecoder();
      let buffer = '';
      let fullContent = '';

      while (true) {
        const { done, value } = await reader.read();
        
        if (done) break;

        buffer += decoder.decode(value, { stream: true });
        const lines = buffer.split('\n');
        buffer = lines.pop() || '';

        for (const line of lines) {
          if (line.startsWith('data: ')) {
            const data = line.slice(6);
            
            if (data === '[DONE]') {
              setIsLoading(false);
              // 更新对话列表
              updateConversationInHistory(userMessage.content);
              return;
            }

            try {
              const parsed = JSON.parse(data);
              if (parsed.content !== undefined) {
                fullContent = parsed.content;
                setMessages(prev => 
                  prev.map(msg => 
                    msg.id === assistantMessageId 
                      ? { ...msg, content: fullContent }
                      : msg
                  )
                );
              }
              
              if (parsed.done) {
                setIsLoading(false);
                updateConversationInHistory(userMessage.content);
                return;
              }
            } catch (e) {
              console.warn('Failed to parse SSE data:', data);
            }
          }
        }
      }

      setIsLoading(false);
    } catch (error) {
      console.error('Error sending message:', error);
      setMessages(prev => 
        prev.map(msg => 
          msg.id === assistantMessageId 
            ? { 
                ...msg, 
                content: 'Sorry, I encountered an error. Please try again.'
              }
            : msg
        )
      );
      setIsLoading(false);
    }
  };

  // 更新对话历史记录
  const updateConversationInHistory = (lastMessage: string) => {
    setConversations(prev => {
      const existingIndex = prev.findIndex(conv => conv.id === currentConversationId);
      const title = generateConversationTitle(lastMessage);
      
      const updatedConversation: Conversation = {
        id: currentConversationId,
        title,
        lastMessage: lastMessage.slice(0, 50) + (lastMessage.length > 50 ? '...' : ''),
        timestamp: new Date(),
        messageCount: messages.length + 2 // +2 for user and assistant message
      };

      if (existingIndex >= 0) {
        const updated = [...prev];
        updated[existingIndex] = updatedConversation;
        return updated.sort((a, b) => b.timestamp.getTime() - a.timestamp.getTime());
      } else {
        return [updatedConversation, ...prev];
      }
    });
  };

  // 生成对话标题
  const generateConversationTitle = (firstMessage: string): string => {
    if (firstMessage.length <= 20) return firstMessage;
    
    // 简单的标题生成逻辑
    const keywords = ['Python', 'React', '机器学习', 'AI', '编程', '开发'];
    const foundKeyword = keywords.find(keyword => 
      firstMessage.toLowerCase().includes(keyword.toLowerCase())
    );
    
    if (foundKeyword) {
      return `关于${foundKeyword}的讨论`;
    }
    
    return firstMessage.slice(0, 20) + '...';
  };

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      handleSendMessage();
    }
  };

  // 格式化时间显示
  const formatTime = (timestamp: Date): string => {
    const now = new Date();
    const diff = now.getTime() - timestamp.getTime();
    const days = Math.floor(diff / (1000 * 60 * 60 * 24));

    if (days === 0) return '今天';
    if (days === 1) return '昨天';
    if (days < 7) return `${days}天前`;

    return timestamp.toLocaleDateString();
  };

  // 侧边栏内容
  const renderSidebar = () => (
    <div className="conversation-sidebar">
      {/* 侧边栏头部 */}
      <div className="sidebar-header">
        <div className="sidebar-title">
          <HistoryOutlined className="sidebar-icon" />
          <Text className="sidebar-title-text">对话历史</Text>
        </div>
        {isMobile && (
          <Button
            type="text"
            icon={<CloseOutlined />}
            onClick={() => setSidebarVisible(false)}
            className="sidebar-close-btn"
          />
        )}
      </div>

      {/* 新建对话按钮 */}
      <Button
        type="primary"
        icon={<PlusOutlined />}
        onClick={handleNewConversation}
        className="new-conversation-btn"
        block
      >
        新建对话
      </Button>

      {/* 对话列表 */}
      <div className="conversation-list">
        {conversations.map((conversation) => (
          <div
            key={conversation.id}
            className={`conversation-item ${
              conversation.id === currentConversationId ? 'active' : ''
            }`}
            onClick={() => handleSwitchConversation(conversation.id)}
          >
            <div className="conversation-content">
              <div className="conversation-title">{conversation.title}</div>
              <div className="conversation-preview">{conversation.lastMessage}</div>
              <div className="conversation-meta">
                <span className="conversation-time">{formatTime(conversation.timestamp)}</span>
                <span className="conversation-count">{conversation.messageCount} 条消息</span>
              </div>
            </div>
          </div>
        ))}

        {conversations.length === 0 && (
          <div className="empty-conversations">
            <Text className="empty-text">暂无历史对话</Text>
          </div>
        )}
      </div>
    </div>
  );

  return (
    <div className="enhanced-chat-container">
      {/* 动态背景 */}
      <div className="chat-background" />

      {/* 桌面端布局 */}
      {!isMobile ? (
        <div className="desktop-layout">
          {/* 左侧边栏 */}
          <div className="desktop-sidebar">
            {renderSidebar()}
          </div>

          {/* 主聊天区域 */}
          <div className="main-chat-area">
            {/* 顶部导航栏 */}
            <Card className="chat-header">
              <div className="header-content">
                <div className="header-info">
                  <Avatar size={40} className="logo-avatar">
                    ✨
                  </Avatar>
                  <div className="header-text">
                    <Title level={2} className="header-title">
                      AutoGen Chat
                    </Title>
                    <Text className="header-subtitle">
                      Powered by AI Agents
                    </Text>
                  </div>
                </div>

                <div className="header-actions">
                  <Button
                    type="primary"
                    icon={<PlusOutlined />}
                    onClick={handleNewConversation}
                    className="header-new-btn"
                  >
                    新对话
                  </Button>
                </div>
              </div>
            </Card>

            {/* 消息区域 */}
            <div className="messages-container">
              {messages.length === 0 ? (
                <div className="welcome-screen">
                  <Avatar size={80} icon={<RobotOutlined />} className="welcome-avatar" />
                  <Title level={2} className="welcome-title">
                    Welcome to AutoGen Chat!
                  </Title>
                  <Text className="welcome-description">
                    Start a conversation with our AI assistant. Ask questions, get help, or just chat!
                  </Text>
                </div>
              ) : (
                <Space direction="vertical" size="large" style={{ width: '100%' }}>
                  {messages.map((message) => (
                    <div key={message.id} className={`message-wrapper ${message.role}-message`}>
                      {message.role === 'assistant' && (
                        <Avatar size={36} icon={<RobotOutlined />} className="assistant-avatar" />
                      )}
                      <Card className={`message-card ${message.role}-card`}>
                        <div className="message-content">
                          {message.role === 'user' ? (
                            <Text className="user-text">{message.content}</Text>
                          ) : (
                            message.content ? (
                              <div className="markdown-content">
                                <ReactMarkdown
                                  components={{
                                    code({ node, inline, className, children, ...props }) {
                                      const match = /language-(\w+)/.exec(className || '');
                                      const language = match ? match[1] : '';

                                      if (!inline && match) {
                                        return (
                                          <div className="code-block-wrapper">
                                            {language && (
                                              <div className="code-language-label">
                                                {language}
                                              </div>
                                            )}
                                            <SyntaxHighlighter
                                              style={vscDarkPlus}
                                              language={language}
                                              PreTag="div"
                                              className="code-block"
                                              showLineNumbers={false}
                                              wrapLines={true}
                                              {...props}
                                            >
                                              {String(children).replace(/\n$/, '')}
                                            </SyntaxHighlighter>
                                          </div>
                                        );
                                      } else {
                                        return (
                                          <code className="inline-code" {...props}>
                                            {children}
                                          </code>
                                        );
                                      }
                                    },
                                    p: ({ children }) => <p className="markdown-paragraph">{children}</p>,
                                    ul: ({ children }) => <ul className="markdown-list">{children}</ul>,
                                    ol: ({ children }) => <ol className="markdown-list">{children}</ol>,
                                    li: ({ children }) => <li className="markdown-list-item">{children}</li>,
                                    h1: ({ children }) => <h1 className="markdown-heading">{children}</h1>,
                                    h2: ({ children }) => <h2 className="markdown-heading">{children}</h2>,
                                    h3: ({ children }) => <h3 className="markdown-heading">{children}</h3>,
                                    h4: ({ children }) => <h4 className="markdown-heading">{children}</h4>,
                                    h5: ({ children }) => <h5 className="markdown-heading">{children}</h5>,
                                    h6: ({ children }) => <h6 className="markdown-heading">{children}</h6>,
                                    blockquote: ({ children }) => <blockquote className="markdown-blockquote">{children}</blockquote>,
                                    strong: ({ children }) => <strong className="markdown-strong">{children}</strong>,
                                    em: ({ children }) => <em className="markdown-em">{children}</em>,
                                    hr: () => <hr className="markdown-hr" />,
                                    table: ({ children }) => <table className="markdown-table">{children}</table>,
                                    thead: ({ children }) => <thead className="markdown-thead">{children}</thead>,
                                    tbody: ({ children }) => <tbody className="markdown-tbody">{children}</tbody>,
                                    tr: ({ children }) => <tr className="markdown-tr">{children}</tr>,
                                    th: ({ children }) => <th className="markdown-th">{children}</th>,
                                    td: ({ children }) => <td className="markdown-td">{children}</td>,
                                    a: ({ href, children }) => (
                                      <a
                                        href={href}
                                        target="_blank"
                                        rel="noopener noreferrer"
                                        className="markdown-link"
                                      >
                                        {children}
                                      </a>
                                    ),
                                    img: ({ src, alt }) => (
                                      <img
                                        src={src}
                                        alt={alt}
                                        className="markdown-image"
                                        loading="lazy"
                                      />
                                    ),
                                  }}
                                >
                                  {message.content}
                                </ReactMarkdown>
                                {isLoading && message.role === 'assistant' && (
                                  <span className="typing-indicator">
                                    <span></span>
                                    <span></span>
                                    <span></span>
                                  </span>
                                )}
                              </div>
                            ) : (
                              isLoading ? (
                                <div className="thinking-indicator">
                                  <Text className="thinking-text">Thinking...</Text>
                                  <div className="typing-indicator">
                                    <span></span>
                                    <span></span>
                                    <span></span>
                                  </div>
                                </div>
                              ) : ''
                            )
                          )}
                        </div>
                      </Card>
                      {message.role === 'user' && (
                        <Avatar size={36} icon={<UserOutlined />} className="user-avatar" />
                      )}
                    </div>
                  ))}
                </Space>
              )}
              <div ref={messagesEndRef} />
            </div>

            {/* 输入区域 */}
            <Card className="input-container">
              <div className="input-wrapper">
                <TextArea
                  value={inputValue}
                  onChange={(e) => setInputValue(e.target.value)}
                  onKeyPress={handleKeyPress}
                  placeholder="Type your message here..."
                  autoSize={{ minRows: 1, maxRows: 4 }}
                  disabled={isLoading}
                  className="message-input"
                />
                <Button
                  type="primary"
                  icon={isLoading ? <Spin size="small" /> : <SendOutlined />}
                  onClick={handleSendMessage}
                  disabled={!inputValue.trim() || isLoading}
                  className="send-button"
                />
              </div>
            </Card>
          </div>
        </div>
      ) : (
        /* 移动端布局 */
        <div className="mobile-layout">
          {/* 移动端顶部导航 */}
          <Card className="mobile-header">
            <div className="mobile-header-content">
              <Button
                type="text"
                icon={<MenuOutlined />}
                onClick={() => setSidebarVisible(true)}
                className="mobile-menu-btn"
              />
              <div className="mobile-header-info">
                <Title level={3} className="mobile-title">AutoGen Chat</Title>
              </div>
              <Button
                type="primary"
                icon={<PlusOutlined />}
                onClick={handleNewConversation}
                className="mobile-new-btn"
              />
            </div>
          </Card>

          {/* 移动端消息区域 */}
          <div className="mobile-messages">
            {messages.length === 0 ? (
              <div className="welcome-screen">
                <Avatar size={60} icon={<RobotOutlined />} className="welcome-avatar" />
                <Title level={3} className="welcome-title">
                  Welcome to AutoGen Chat!
                </Title>
                <Text className="welcome-description">
                  Start a conversation with our AI assistant.
                </Text>
              </div>
            ) : (
              <Space direction="vertical" size="large" style={{ width: '100%' }}>
                {messages.map((message) => (
                  <div key={message.id} className={`message-wrapper ${message.role}-message`}>
                    {message.role === 'assistant' && (
                      <Avatar size={32} icon={<RobotOutlined />} className="assistant-avatar" />
                    )}
                    <Card className={`message-card ${message.role}-card`}>
                      <div className="message-content">
                        {message.role === 'user' ? (
                          <Text className="user-text">{message.content}</Text>
                        ) : (
                          message.content ? (
                            <div className="markdown-content">
                              <ReactMarkdown
                                components={{
                                  code({ node, inline, className, children, ...props }) {
                                    const match = /language-(\w+)/.exec(className || '');
                                    const language = match ? match[1] : '';

                                    if (!inline && match) {
                                      return (
                                        <div className="code-block-wrapper">
                                          {language && (
                                            <div className="code-language-label">
                                              {language}
                                            </div>
                                          )}
                                          <SyntaxHighlighter
                                            style={vscDarkPlus}
                                            language={language}
                                            PreTag="div"
                                            className="code-block"
                                            showLineNumbers={false}
                                            wrapLines={true}
                                            {...props}
                                          >
                                            {String(children).replace(/\n$/, '')}
                                          </SyntaxHighlighter>
                                        </div>
                                      );
                                    } else {
                                      return (
                                        <code className="inline-code" {...props}>
                                          {children}
                                        </code>
                                      );
                                    }
                                  },
                                  p: ({ children }) => <p className="markdown-paragraph">{children}</p>,
                                  ul: ({ children }) => <ul className="markdown-list">{children}</ul>,
                                  ol: ({ children }) => <ol className="markdown-list">{children}</ol>,
                                  li: ({ children }) => <li className="markdown-list-item">{children}</li>,
                                  h1: ({ children }) => <h1 className="markdown-heading">{children}</h1>,
                                  h2: ({ children }) => <h2 className="markdown-heading">{children}</h2>,
                                  h3: ({ children }) => <h3 className="markdown-heading">{children}</h3>,
                                  h4: ({ children }) => <h4 className="markdown-heading">{children}</h4>,
                                  h5: ({ children }) => <h5 className="markdown-heading">{children}</h5>,
                                  h6: ({ children }) => <h6 className="markdown-heading">{children}</h6>,
                                  blockquote: ({ children }) => <blockquote className="markdown-blockquote">{children}</blockquote>,
                                  strong: ({ children }) => <strong className="markdown-strong">{children}</strong>,
                                  em: ({ children }) => <em className="markdown-em">{children}</em>,
                                  hr: () => <hr className="markdown-hr" />,
                                  table: ({ children }) => <table className="markdown-table">{children}</table>,
                                  thead: ({ children }) => <thead className="markdown-thead">{children}</thead>,
                                  tbody: ({ children }) => <tbody className="markdown-tbody">{children}</tbody>,
                                  tr: ({ children }) => <tr className="markdown-tr">{children}</tr>,
                                  th: ({ children }) => <th className="markdown-th">{children}</th>,
                                  td: ({ children }) => <td className="markdown-td">{children}</td>,
                                  a: ({ href, children }) => (
                                    <a
                                      href={href}
                                      target="_blank"
                                      rel="noopener noreferrer"
                                      className="markdown-link"
                                    >
                                      {children}
                                    </a>
                                  ),
                                  img: ({ src, alt }) => (
                                    <img
                                      src={src}
                                      alt={alt}
                                      className="markdown-image"
                                      loading="lazy"
                                    />
                                  ),
                                }}
                              >
                                {message.content}
                              </ReactMarkdown>
                              {isLoading && message.role === 'assistant' && (
                                <span className="typing-indicator">
                                  <span></span>
                                  <span></span>
                                  <span></span>
                                </span>
                              )}
                            </div>
                          ) : (
                            isLoading && (
                              <div className="thinking-indicator">
                                <Text className="thinking-text">Thinking...</Text>
                                <div className="typing-indicator">
                                  <span></span>
                                  <span></span>
                                  <span></span>
                                </div>
                              </div>
                            )
                          )
                        )}
                      </div>
                    </Card>
                    {message.role === 'user' && (
                      <Avatar size={32} icon={<UserOutlined />} className="user-avatar" />
                    )}
                  </div>
                ))}
              </Space>
            )}
            <div ref={messagesEndRef} />
          </div>

          {/* 移动端输入区域 */}
          <Card className="mobile-input">
            <div className="input-wrapper">
              <TextArea
                value={inputValue}
                onChange={(e) => setInputValue(e.target.value)}
                onKeyPress={handleKeyPress}
                placeholder="Type your message..."
                autoSize={{ minRows: 1, maxRows: 3 }}
                disabled={isLoading}
                className="message-input"
              />
              <Button
                type="primary"
                icon={isLoading ? <Spin size="small" /> : <SendOutlined />}
                onClick={handleSendMessage}
                disabled={!inputValue.trim() || isLoading}
                className="send-button"
              />
            </div>
          </Card>

          {/* 移动端侧边栏抽屉 */}
          <Drawer
            title={null}
            placement="left"
            onClose={() => setSidebarVisible(false)}
            open={sidebarVisible}
            className="mobile-drawer"
            width={280}
          >
            {renderSidebar()}
          </Drawer>
        </div>
      )}
    </div>
  );
};

export default EnhancedChatApp;
