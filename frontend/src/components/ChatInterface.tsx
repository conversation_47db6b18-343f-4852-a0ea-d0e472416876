import React, { useState, useRef, useEffect } from 'react';
import { Layout, Input, Button, Typography, Space, Card, Avatar, Spin } from 'antd';
import { SendOutlined, RobotOutlined, UserOutlined, SparklesOutlined } from '@ant-design/icons';
import MessageList from './MessageList';
import { chatService } from '../services/chatService';
import './ChatInterface.css';

const { Header, Content, Footer } = Layout;
const { Title, Text } = Typography;
const { TextArea } = Input;

interface Message {
  id: string;
  role: 'user' | 'assistant';
  content: string;
  timestamp: Date;
  streaming?: boolean;
}

const ChatInterface: React.FC = () => {
  const [messages, setMessages] = useState<Message[]>([]);
  const [inputValue, setInputValue] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [conversationId] = useState(() => `conv_${Date.now()}`);
  const messagesEndRef = useRef<HTMLDivElement>(null);

  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  };

  useEffect(() => {
    scrollToBottom();
  }, [messages]);

  const handleSendMessage = async () => {
    if (!inputValue.trim() || isLoading) return;

    const userMessage: Message = {
      id: `msg_${Date.now()}`,
      role: 'user',
      content: inputValue.trim(),
      timestamp: new Date(),
    };

    setMessages(prev => [...prev, userMessage]);
    setInputValue('');
    setIsLoading(true);

    // 创建助手消息占位符
    const assistantMessageId = `msg_${Date.now() + 1}`;
    const assistantMessage: Message = {
      id: assistantMessageId,
      role: 'assistant',
      content: '',
      timestamp: new Date(),
      streaming: true,
    };

    setMessages(prev => [...prev, assistantMessage]);

    try {
      await chatService.sendMessage(
        userMessage.content,
        conversationId,
        (chunk: string) => {
          setMessages(prev => 
            prev.map(msg => 
              msg.id === assistantMessageId 
                ? { ...msg, content: chunk }
                : msg
            )
          );
        },
        () => {
          setMessages(prev => 
            prev.map(msg => 
              msg.id === assistantMessageId 
                ? { ...msg, streaming: false }
                : msg
            )
          );
          setIsLoading(false);
        }
      );
    } catch (error) {
      console.error('Error sending message:', error);
      setMessages(prev => 
        prev.map(msg => 
          msg.id === assistantMessageId 
            ? { 
                ...msg, 
                content: 'Sorry, I encountered an error. Please try again.',
                streaming: false 
              }
            : msg
        )
      );
      setIsLoading(false);
    }
  };

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      handleSendMessage();
    }
  };

  return (
    <Layout className="chat-layout">
      <Header className="chat-header">
        <div className="header-content">
          <Space align="center">
            <Avatar 
              size={40} 
              icon={<SparklesOutlined />} 
              className="logo-avatar"
            />
            <div>
              <Title level={3} className="header-title">
                AutoGen Chat
              </Title>
              <Text className="header-subtitle">
                Powered by AI Agents
              </Text>
            </div>
          </Space>
          <div className="header-status">
            <Space>
              <div className="status-indicator" />
              <Text className="status-text">Online</Text>
            </Space>
          </div>
        </div>
      </Header>

      <Content className="chat-content">
        <div className="messages-container">
          {messages.length === 0 ? (
            <div className="welcome-screen">
              <div className="welcome-content">
                <Avatar 
                  size={80} 
                  icon={<RobotOutlined />} 
                  className="welcome-avatar"
                />
                <Title level={2} className="welcome-title">
                  Welcome to AutoGen Chat
                </Title>
                <Text className="welcome-description">
                  Start a conversation with our AI assistant. Ask questions, get help, or just chat!
                </Text>
                <div className="suggestion-cards">
                  <Card 
                    className="suggestion-card"
                    onClick={() => setInputValue("What can you help me with?")}
                  >
                    <Text>What can you help me with?</Text>
                  </Card>
                  <Card 
                    className="suggestion-card"
                    onClick={() => setInputValue("Explain machine learning")}
                  >
                    <Text>Explain machine learning</Text>
                  </Card>
                  <Card 
                    className="suggestion-card"
                    onClick={() => setInputValue("Write a Python function")}
                  >
                    <Text>Write a Python function</Text>
                  </Card>
                </div>
              </div>
            </div>
          ) : (
            <MessageList messages={messages} />
          )}
          <div ref={messagesEndRef} />
        </div>
      </Content>

      <Footer className="chat-footer">
        <div className="input-container">
          <div className="input-wrapper">
            <TextArea
              value={inputValue}
              onChange={(e) => setInputValue(e.target.value)}
              onKeyPress={handleKeyPress}
              placeholder="Type your message here... (Press Enter to send, Shift+Enter for new line)"
              autoSize={{ minRows: 1, maxRows: 4 }}
              className="message-input"
              disabled={isLoading}
            />
            <Button
              type="primary"
              icon={isLoading ? <Spin size="small" /> : <SendOutlined />}
              onClick={handleSendMessage}
              disabled={!inputValue.trim() || isLoading}
              className="send-button"
              size="large"
            />
          </div>
        </div>
      </Footer>
    </Layout>
  );
};

export default ChatInterface;
