.chat-layout {
  height: 100vh;
  background: transparent;
}

.chat-header {
  background: rgba(26, 26, 26, 0.95);
  backdrop-filter: blur(20px);
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
  padding: 0 24px;
  height: 80px;
  display: flex;
  align-items: center;
  position: relative;
  z-index: 10;
}

.header-content {
  width: 100%;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.logo-avatar {
  background: linear-gradient(135deg, #4285f4, #34a853);
  border: 2px solid rgba(255, 255, 255, 0.2);
}

.header-title {
  color: #ffffff !important;
  margin: 0 !important;
  font-weight: 600;
  font-size: 20px;
}

.header-subtitle {
  color: #b3b3b3;
  font-size: 12px;
}

.header-status {
  display: flex;
  align-items: center;
}

.status-indicator {
  width: 8px;
  height: 8px;
  background: #34a853;
  border-radius: 50%;
  margin-right: 8px;
  animation: pulse 2s infinite;
}

@keyframes pulse {
  0% {
    box-shadow: 0 0 0 0 rgba(52, 168, 83, 0.7);
  }
  70% {
    box-shadow: 0 0 0 10px rgba(52, 168, 83, 0);
  }
  100% {
    box-shadow: 0 0 0 0 rgba(52, 168, 83, 0);
  }
}

.status-text {
  color: #34a853;
  font-size: 12px;
  font-weight: 500;
}

.chat-content {
  background: transparent;
  padding: 0;
  height: calc(100vh - 160px);
  overflow: hidden;
}

.messages-container {
  height: 100%;
  overflow-y: auto;
  padding: 20px;
  scroll-behavior: smooth;
}

.welcome-screen {
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
}

.welcome-content {
  text-align: center;
  max-width: 600px;
  padding: 40px;
}

.welcome-avatar {
  background: linear-gradient(135deg, #4285f4, #34a853);
  margin-bottom: 24px;
  border: 3px solid rgba(255, 255, 255, 0.1);
}

.welcome-title {
  color: #ffffff !important;
  margin-bottom: 16px !important;
  font-weight: 600;
}

.welcome-description {
  color: #b3b3b3;
  font-size: 16px;
  line-height: 1.6;
  margin-bottom: 32px;
}

.suggestion-cards {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 16px;
  margin-top: 32px;
}

.suggestion-card {
  background: rgba(255, 255, 255, 0.05) !important;
  border: 1px solid rgba(255, 255, 255, 0.1) !important;
  border-radius: 12px !important;
  cursor: pointer;
  transition: all 0.3s ease;
  backdrop-filter: blur(10px);
}

.suggestion-card:hover {
  background: rgba(255, 255, 255, 0.1) !important;
  border-color: rgba(66, 133, 244, 0.5) !important;
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(66, 133, 244, 0.2);
}

.suggestion-card .ant-card-body {
  padding: 16px !important;
}

.suggestion-card span {
  color: #ffffff;
  font-size: 14px;
}

.chat-footer {
  background: rgba(26, 26, 26, 0.95);
  backdrop-filter: blur(20px);
  border-top: 1px solid rgba(255, 255, 255, 0.1);
  padding: 20px 24px;
  height: 80px;
  display: flex;
  align-items: center;
}

.input-container {
  width: 100%;
  max-width: 800px;
  margin: 0 auto;
}

.input-wrapper {
  display: flex;
  align-items: flex-end;
  gap: 12px;
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 24px;
  padding: 8px 8px 8px 20px;
  backdrop-filter: blur(10px);
  transition: all 0.3s ease;
}

.input-wrapper:focus-within {
  border-color: rgba(66, 133, 244, 0.5);
  box-shadow: 0 0 0 2px rgba(66, 133, 244, 0.1);
}

.message-input {
  flex: 1;
  background: transparent !important;
  border: none !important;
  box-shadow: none !important;
  color: #ffffff !important;
  font-size: 14px;
  resize: none;
}

.message-input::placeholder {
  color: #666666;
}

.message-input:focus {
  box-shadow: none !important;
}

.send-button {
  background: linear-gradient(135deg, #4285f4, #34a853) !important;
  border: none !important;
  border-radius: 20px !important;
  width: 40px !important;
  height: 40px !important;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
  transition: all 0.3s ease !important;
  box-shadow: 0 4px 12px rgba(66, 133, 244, 0.3) !important;
}

.send-button:hover {
  transform: translateY(-1px) !important;
  box-shadow: 0 6px 16px rgba(66, 133, 244, 0.4) !important;
}

.send-button:disabled {
  background: rgba(255, 255, 255, 0.1) !important;
  box-shadow: none !important;
  transform: none !important;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .chat-header {
    padding: 0 16px;
    height: 70px;
  }
  
  .header-title {
    font-size: 18px;
  }
  
  .messages-container {
    padding: 16px;
  }
  
  .chat-footer {
    padding: 16px;
    height: 70px;
  }
  
  .suggestion-cards {
    grid-template-columns: 1fr;
  }
  
  .welcome-content {
    padding: 20px;
  }
}
