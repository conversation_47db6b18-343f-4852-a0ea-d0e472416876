import React, { useState } from 'react';
import { Button, Input, Card, Typography, Space, Avatar } from 'antd';
import { SendOutlined, RobotOutlined, UserOutlined } from '@ant-design/icons';

const { Title, Text } = Typography;
const { TextArea } = Input;

interface Message {
  id: string;
  role: 'user' | 'assistant';
  content: string;
  timestamp: Date;
}

const SimpleChat: React.FC = () => {
  const [messages, setMessages] = useState<Message[]>([]);
  const [inputValue, setInputValue] = useState('');
  const [isLoading, setIsLoading] = useState(false);

  const handleSendMessage = async () => {
    if (!inputValue.trim() || isLoading) return;

    const userMessage: Message = {
      id: `msg_${Date.now()}`,
      role: 'user',
      content: inputValue.trim(),
      timestamp: new Date(),
    };

    setMessages(prev => [...prev, userMessage]);
    setInputValue('');
    setIsLoading(true);

    // 创建助手消息占位符
    const assistantMessageId = `msg_${Date.now() + 1}`;
    const assistantMessage: Message = {
      id: assistantMessageId,
      role: 'assistant',
      content: '',
      timestamp: new Date(),
    };

    setMessages(prev => [...prev, assistantMessage]);

    try {
      // 调用后端API
      const response = await fetch('http://localhost:8000/api/chat/stream', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          message: userMessage.content,
          conversation_id: `conv_${Date.now()}`
        }),
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const reader = response.body?.getReader();
      if (!reader) {
        throw new Error('No response body reader available');
      }

      const decoder = new TextDecoder();
      let buffer = '';
      let fullContent = '';

      while (true) {
        const { done, value } = await reader.read();

        if (done) {
          break;
        }

        buffer += decoder.decode(value, { stream: true });
        const lines = buffer.split('\n');
        buffer = lines.pop() || '';

        for (const line of lines) {
          if (line.startsWith('data: ')) {
            const data = line.slice(6);

            if (data === '[DONE]') {
              setIsLoading(false);
              return;
            }

            try {
              const parsed = JSON.parse(data);
              if (parsed.content !== undefined) {
                fullContent = parsed.content;
                setMessages(prev =>
                  prev.map(msg =>
                    msg.id === assistantMessageId
                      ? { ...msg, content: fullContent }
                      : msg
                  )
                );
              }

              if (parsed.done) {
                setIsLoading(false);
                return;
              }
            } catch (e) {
              console.warn('Failed to parse SSE data:', data);
            }
          }
        }
      }

      setIsLoading(false);
    } catch (error) {
      console.error('Error sending message:', error);
      setMessages(prev =>
        prev.map(msg =>
          msg.id === assistantMessageId
            ? {
                ...msg,
                content: 'Sorry, I encountered an error. Please try again.'
              }
            : msg
        )
      );
      setIsLoading(false);
    }
  };

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      handleSendMessage();
    }
  };

  return (
    <div style={{
      height: '100vh',
      background: 'linear-gradient(135deg, #0f0f0f 0%, #1a1a1a 50%, #0f0f0f 100%)',
      position: 'relative',
      overflow: 'hidden'
    }}>
      {/* 动态背景效果 */}
      <div style={{
        position: 'absolute',
        top: 0,
        left: 0,
        right: 0,
        bottom: 0,
        background: `
          radial-gradient(circle at 20% 80%, rgba(66, 133, 244, 0.1) 0%, transparent 50%),
          radial-gradient(circle at 80% 20%, rgba(234, 67, 53, 0.1) 0%, transparent 50%),
          radial-gradient(circle at 40% 40%, rgba(52, 168, 83, 0.1) 0%, transparent 50%)
        `,
        animation: 'gradientShift 20s ease-in-out infinite',
        pointerEvents: 'none'
      }} />

      <div style={{
        position: 'relative',
        zIndex: 1,
        height: '100%',
        display: 'flex',
        flexDirection: 'column',
        padding: '20px',
        color: 'white'
      }}>
        {/* Header */}
        <div style={{
          textAlign: 'center',
          marginBottom: '20px',
          padding: '20px',
          background: 'rgba(26, 26, 26, 0.95)',
          borderRadius: '12px',
          backdropFilter: 'blur(20px)',
          border: '1px solid rgba(255, 255, 255, 0.1)',
          boxShadow: '0 8px 32px rgba(0, 0, 0, 0.3)'
        }}>
          <div style={{ display: 'flex', alignItems: 'center', justifyContent: 'center', gap: '12px' }}>
            <Avatar size={40} style={{
              background: 'linear-gradient(135deg, #4285f4, #34a853)',
              border: '2px solid rgba(255, 255, 255, 0.2)'
            }}>
              ✨
            </Avatar>
            <div>
              <Title level={2} style={{ color: 'white', margin: 0, fontWeight: 600 }}>
                AutoGen Chat
              </Title>
              <Text style={{ color: '#b3b3b3', fontSize: '12px' }}>
                Powered by AI Agents
              </Text>
            </div>
            <div style={{
              display: 'flex',
              alignItems: 'center',
              gap: '8px',
              marginLeft: '20px'
            }}>
              <div style={{
                width: '8px',
                height: '8px',
                background: '#34a853',
                borderRadius: '50%',
                animation: 'pulse 2s infinite'
              }} />
              <Text style={{ color: '#34a853', fontSize: '12px', fontWeight: 500 }}>
                Online
              </Text>
            </div>
          </div>
        </div>

      {/* Messages */}
      <div style={{
        flex: 1,
        overflowY: 'auto',
        marginBottom: '20px',
        padding: '10px'
      }}>
        {messages.length === 0 ? (
          <div style={{
            textAlign: 'center',
            marginTop: '50px',
            animation: 'messageSlideIn 0.5s ease-out'
          }}>
            <Avatar size={80} icon={<RobotOutlined />} style={{
              background: 'linear-gradient(135deg, #4285f4, #34a853)',
              marginBottom: '24px',
              border: '3px solid rgba(255, 255, 255, 0.1)',
              boxShadow: '0 8px 32px rgba(66, 133, 244, 0.3)'
            }} />
            <Title level={2} style={{ color: 'white', marginBottom: '16px', fontWeight: 600 }}>
              Welcome to AutoGen Chat!
            </Title>
            <Text style={{ color: '#b3b3b3', fontSize: '16px', lineHeight: 1.6 }}>
              Start a conversation with our AI assistant. Ask questions, get help, or just chat!
            </Text>

            {/* 建议卡片 */}
            <div style={{
              display: 'grid',
              gridTemplateColumns: 'repeat(auto-fit, minmax(200px, 1fr))',
              gap: '16px',
              marginTop: '32px',
              maxWidth: '600px',
              margin: '32px auto 0'
            }}>
              {[
                "What can you help me with?",
                "Explain machine learning",
                "Write a Python function"
              ].map((suggestion, index) => (
                <Card
                  key={index}
                  hoverable
                  onClick={() => setInputValue(suggestion)}
                  style={{
                    background: 'rgba(255, 255, 255, 0.05)',
                    border: '1px solid rgba(255, 255, 255, 0.1)',
                    borderRadius: '12px',
                    cursor: 'pointer',
                    transition: 'all 0.3s ease',
                    backdropFilter: 'blur(10px)'
                  }}
                  onMouseEnter={(e) => {
                    e.currentTarget.style.background = 'rgba(255, 255, 255, 0.1)';
                    e.currentTarget.style.borderColor = 'rgba(66, 133, 244, 0.5)';
                    e.currentTarget.style.transform = 'translateY(-2px)';
                    e.currentTarget.style.boxShadow = '0 8px 25px rgba(66, 133, 244, 0.2)';
                  }}
                  onMouseLeave={(e) => {
                    e.currentTarget.style.background = 'rgba(255, 255, 255, 0.05)';
                    e.currentTarget.style.borderColor = 'rgba(255, 255, 255, 0.1)';
                    e.currentTarget.style.transform = 'translateY(0)';
                    e.currentTarget.style.boxShadow = 'none';
                  }}
                >
                  <Text style={{ color: 'white', fontSize: '14px' }}>{suggestion}</Text>
                </Card>
              ))}
            </div>
          </div>
        ) : (
          <Space direction="vertical" size="large" style={{ width: '100%' }}>
            {messages.map((message, index) => (
              <div key={message.id} style={{
                display: 'flex',
                justifyContent: message.role === 'user' ? 'flex-end' : 'flex-start',
                gap: '12px',
                animation: 'messageSlideIn 0.3s ease-out',
                animationDelay: `${index * 0.1}s`,
                animationFillMode: 'both'
              }}>
                {message.role === 'assistant' && (
                  <Avatar size={36} icon={<RobotOutlined />} style={{
                    background: 'linear-gradient(135deg, #34a853, #137333)',
                    border: '2px solid rgba(255, 255, 255, 0.1)',
                    marginTop: '4px'
                  }} />
                )}
                <Card style={{
                  maxWidth: '80%',
                  background: message.role === 'user'
                    ? 'linear-gradient(135deg, rgba(66, 133, 244, 0.2), rgba(26, 115, 232, 0.2))'
                    : 'rgba(255, 255, 255, 0.05)',
                  border: message.role === 'user'
                    ? '1px solid rgba(66, 133, 244, 0.3)'
                    : '1px solid rgba(255, 255, 255, 0.1)',
                  borderRadius: '18px',
                  backdropFilter: 'blur(10px)',
                  transition: 'all 0.3s ease',
                  boxShadow: '0 4px 12px rgba(0, 0, 0, 0.1)'
                }}>
                  <div style={{ position: 'relative' }}>
                    <Text style={{ color: 'white', lineHeight: 1.6 }}>
                      {message.content}
                    </Text>
                    {isLoading && message.role === 'assistant' && message.content === '' && (
                      <div style={{
                        display: 'inline-flex',
                        alignItems: 'center',
                        gap: '4px'
                      }}>
                        {[0, 1, 2].map((i) => (
                          <div
                            key={i}
                            style={{
                              width: '4px',
                              height: '4px',
                              background: 'rgba(66, 133, 244, 0.7)',
                              borderRadius: '50%',
                              animation: 'typing 1.4s infinite ease-in-out',
                              animationDelay: `${i * 0.16}s`
                            }}
                          />
                        ))}
                      </div>
                    )}
                  </div>
                </Card>
                {message.role === 'user' && (
                  <Avatar size={36} icon={<UserOutlined />} style={{
                    background: 'linear-gradient(135deg, #4285f4, #1a73e8)',
                    border: '2px solid rgba(255, 255, 255, 0.1)',
                    marginTop: '4px'
                  }} />
                )}
              </div>
            ))}
          </Space>
        )}
      </div>

        {/* Input */}
        <div style={{
          maxWidth: '800px',
          margin: '0 auto',
          width: '100%'
        }}>
          <div style={{
            display: 'flex',
            alignItems: 'flex-end',
            gap: '12px',
            background: 'rgba(26, 26, 26, 0.95)',
            border: '1px solid rgba(255, 255, 255, 0.1)',
            borderRadius: '24px',
            padding: '8px 8px 8px 20px',
            backdropFilter: 'blur(20px)',
            transition: 'all 0.3s ease',
            boxShadow: '0 4px 12px rgba(0, 0, 0, 0.2)'
          }}>
            <TextArea
              value={inputValue}
              onChange={(e) => setInputValue(e.target.value)}
              onKeyPress={handleKeyPress}
              placeholder="Type your message here... (Press Enter to send, Shift+Enter for new line)"
              autoSize={{ minRows: 1, maxRows: 4 }}
              disabled={isLoading}
              style={{
                flex: 1,
                background: 'transparent',
                border: 'none',
                color: 'white',
                resize: 'none',
                fontSize: '14px'
              }}
              onFocus={(e) => {
                e.target.parentElement.style.borderColor = 'rgba(66, 133, 244, 0.5)';
                e.target.parentElement.style.boxShadow = '0 0 0 2px rgba(66, 133, 244, 0.1)';
              }}
              onBlur={(e) => {
                e.target.parentElement.style.borderColor = 'rgba(255, 255, 255, 0.1)';
                e.target.parentElement.style.boxShadow = '0 4px 12px rgba(0, 0, 0, 0.2)';
              }}
            />
            <Button
              type="primary"
              icon={isLoading ? <div style={{
                width: '14px',
                height: '14px',
                border: '2px solid rgba(255, 255, 255, 0.3)',
                borderTop: '2px solid white',
                borderRadius: '50%',
                animation: 'spin 1s linear infinite'
              }} /> : <SendOutlined />}
              onClick={handleSendMessage}
              disabled={!inputValue.trim() || isLoading}
              style={{
                background: 'linear-gradient(135deg, #4285f4, #34a853)',
                border: 'none',
                borderRadius: '20px',
                width: '40px',
                height: '40px',
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
                transition: 'all 0.3s ease',
                boxShadow: '0 4px 12px rgba(66, 133, 244, 0.3)'
              }}
              onMouseEnter={(e) => {
                if (!isLoading && inputValue.trim()) {
                  e.currentTarget.style.transform = 'translateY(-1px)';
                  e.currentTarget.style.boxShadow = '0 6px 16px rgba(66, 133, 244, 0.4)';
                }
              }}
              onMouseLeave={(e) => {
                e.currentTarget.style.transform = 'translateY(0)';
                e.currentTarget.style.boxShadow = '0 4px 12px rgba(66, 133, 244, 0.3)';
              }}
            />
          </div>
        </div>
      </div>
    </div>
  );
};

export default SimpleChat;
