import React, { useState, useEffect, useRef } from 'react';
import { Button, Input, Card, Typography, Space, Avatar, Spin } from 'antd';
import { SendOutlined, RobotOutlined, UserOutlined } from '@ant-design/icons';
import ReactMarkdown from 'react-markdown';
import { Prism as SyntaxHighlighter } from 'react-syntax-highlighter';
import { vscDarkPlus } from 'react-syntax-highlighter/dist/esm/styles/prism';
import './ChatApp.css';

const { Title, Text } = Typography;
const { TextArea } = Input;

interface Message {
  id: string;
  role: 'user' | 'assistant';
  content: string;
  timestamp: Date;
}

const ChatApp: React.FC = () => {
  const [messages, setMessages] = useState<Message[]>([]);
  const [inputValue, setInputValue] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const messagesEndRef = useRef<HTMLDivElement>(null);

  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  };

  useEffect(() => {
    scrollToBottom();
  }, [messages]);

  const handleSendMessage = async () => {
    if (!inputValue.trim() || isLoading) return;

    const userMessage: Message = {
      id: `msg_${Date.now()}`,
      role: 'user',
      content: inputValue.trim(),
      timestamp: new Date(),
    };

    setMessages(prev => [...prev, userMessage]);
    setInputValue('');
    setIsLoading(true);

    // 创建助手消息占位符
    const assistantMessageId = `msg_${Date.now() + 1}`;
    const assistantMessage: Message = {
      id: assistantMessageId,
      role: 'assistant',
      content: '',
      timestamp: new Date(),
    };

    setMessages(prev => [...prev, assistantMessage]);

    try {
      const response = await fetch('http://localhost:8000/api/chat/stream', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          message: userMessage.content,
          conversation_id: `conv_${Date.now()}`
        }),
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const reader = response.body?.getReader();
      if (!reader) {
        throw new Error('No response body reader available');
      }

      const decoder = new TextDecoder();
      let buffer = '';
      let fullContent = '';

      while (true) {
        const { done, value } = await reader.read();
        
        if (done) break;

        buffer += decoder.decode(value, { stream: true });
        const lines = buffer.split('\n');
        buffer = lines.pop() || '';

        for (const line of lines) {
          if (line.startsWith('data: ')) {
            const data = line.slice(6);
            
            if (data === '[DONE]') {
              setIsLoading(false);
              return;
            }

            try {
              const parsed = JSON.parse(data);
              if (parsed.content !== undefined) {
                fullContent = parsed.content;
                setMessages(prev => 
                  prev.map(msg => 
                    msg.id === assistantMessageId 
                      ? { ...msg, content: fullContent }
                      : msg
                  )
                );
              }
              
              if (parsed.done) {
                setIsLoading(false);
                return;
              }
            } catch (e) {
              console.warn('Failed to parse SSE data:', data);
            }
          }
        }
      }

      setIsLoading(false);
    } catch (error) {
      console.error('Error sending message:', error);
      setMessages(prev => 
        prev.map(msg => 
          msg.id === assistantMessageId 
            ? { 
                ...msg, 
                content: 'Sorry, I encountered an error. Please try again.'
              }
            : msg
        )
      );
      setIsLoading(false);
    }
  };

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      handleSendMessage();
    }
  };

  return (
    <div style={{
      height: '100vh',
      background: 'linear-gradient(135deg, #0f0f0f 0%, #1a1a1a 50%, #0f0f0f 100%)',
      display: 'flex',
      flexDirection: 'column',
      position: 'relative'
    }}>
      {/* 动态背景 */}
      <div style={{
        position: 'absolute',
        top: 0,
        left: 0,
        right: 0,
        bottom: 0,
        background: `
          radial-gradient(circle at 20% 80%, rgba(66, 133, 244, 0.1) 0%, transparent 50%),
          radial-gradient(circle at 80% 20%, rgba(234, 67, 53, 0.1) 0%, transparent 50%),
          radial-gradient(circle at 40% 40%, rgba(52, 168, 83, 0.1) 0%, transparent 50%)
        `,
        animation: 'gradientShift 20s ease-in-out infinite',
        pointerEvents: 'none'
      }} />

      {/* 内容区域 */}
      <div style={{
        position: 'relative',
        zIndex: 1,
        height: '100%',
        display: 'flex',
        flexDirection: 'column',
        padding: '20px'
      }}>
        {/* Header */}
        <Card style={{
          background: 'rgba(26, 26, 26, 0.95)',
          border: '1px solid rgba(255, 255, 255, 0.1)',
          borderRadius: '12px',
          marginBottom: '20px',
          backdropFilter: 'blur(20px)'
        }}>
          <div style={{ display: 'flex', alignItems: 'center', justifyContent: 'center', gap: '12px' }}>
            <Avatar size={40} style={{
              background: 'linear-gradient(135deg, #4285f4, #34a853)',
              border: '2px solid rgba(255, 255, 255, 0.2)'
            }}>
              ✨
            </Avatar>
            <div style={{ textAlign: 'center' }}>
              <Title level={2} style={{ color: 'white', margin: 0 }}>
                AutoGen Chat
              </Title>
              <Text style={{ color: '#b3b3b3' }}>
                Powered by AI Agents
              </Text>
            </div>
          </div>
        </Card>

        {/* Messages */}
        <div style={{
          flex: 1,
          overflowY: 'auto',
          marginBottom: '20px',
          padding: '10px'
        }}>
          {messages.length === 0 ? (
            <div style={{ textAlign: 'center', marginTop: '50px' }}>
              <Avatar size={80} icon={<RobotOutlined />} style={{
                background: 'linear-gradient(135deg, #4285f4, #34a853)',
                marginBottom: '24px',
                border: '3px solid rgba(255, 255, 255, 0.1)'
              }} />
              <Title level={2} style={{ color: 'white', marginBottom: '16px' }}>
                Welcome to AutoGen Chat!
              </Title>
              <Text style={{ color: '#b3b3b3', fontSize: '16px' }}>
                Start a conversation with our AI assistant. Ask questions, get help, or just chat!
              </Text>
            </div>
          ) : (
            <Space direction="vertical" size="large" style={{ width: '100%' }}>
              {messages.map((message) => (
                <div key={message.id} style={{
                  display: 'flex',
                  justifyContent: message.role === 'user' ? 'flex-end' : 'flex-start',
                  gap: '12px'
                }}>
                  {message.role === 'assistant' && (
                    <Avatar size={36} icon={<RobotOutlined />} style={{
                      background: 'linear-gradient(135deg, #34a853, #137333)',
                      border: '2px solid rgba(255, 255, 255, 0.1)'
                    }} />
                  )}
                  <Card style={{
                    maxWidth: '80%',
                    background: message.role === 'user'
                      ? 'linear-gradient(135deg, rgba(66, 133, 244, 0.2), rgba(26, 115, 232, 0.2))'
                      : 'rgba(255, 255, 255, 0.05)',
                    border: message.role === 'user'
                      ? '1px solid rgba(66, 133, 244, 0.3)'
                      : '1px solid rgba(255, 255, 255, 0.1)',
                    borderRadius: '18px',
                    backdropFilter: 'blur(10px)'
                  }}>
                    <div style={{ color: 'white', lineHeight: 1.6 }}>
                      {message.role === 'user' ? (
                        <Text style={{ color: 'white' }}>{message.content}</Text>
                      ) : (
                        message.content ? (
                          <div className="markdown-content">
                            <ReactMarkdown
                              components={{
                                code({ node, inline, className, children, ...props }) {
                                  const match = /language-(\w+)/.exec(className || '');
                                  return !inline && match ? (
                                    <SyntaxHighlighter
                                      style={vscDarkPlus}
                                      language={match[1]}
                                      PreTag="div"
                                      className="code-block"
                                      {...props}
                                    >
                                      {String(children).replace(/\n$/, '')}
                                    </SyntaxHighlighter>
                                  ) : (
                                    <code className="inline-code" {...props}>
                                      {children}
                                    </code>
                                  );
                                },
                                p: ({ children }) => <p className="markdown-paragraph">{children}</p>,
                                ul: ({ children }) => <ul className="markdown-list">{children}</ul>,
                                ol: ({ children }) => <ol className="markdown-list">{children}</ol>,
                                li: ({ children }) => <li className="markdown-list-item">{children}</li>,
                                h1: ({ children }) => <h1 className="markdown-heading">{children}</h1>,
                                h2: ({ children }) => <h2 className="markdown-heading">{children}</h2>,
                                h3: ({ children }) => <h3 className="markdown-heading">{children}</h3>,
                                h4: ({ children }) => <h4 className="markdown-heading">{children}</h4>,
                                h5: ({ children }) => <h5 className="markdown-heading">{children}</h5>,
                                h6: ({ children }) => <h6 className="markdown-heading">{children}</h6>,
                                blockquote: ({ children }) => <blockquote className="markdown-blockquote">{children}</blockquote>,
                                strong: ({ children }) => <strong className="markdown-strong">{children}</strong>,
                                em: ({ children }) => <em className="markdown-em">{children}</em>,
                                hr: () => <hr className="markdown-hr" />,
                                table: ({ children }) => <table className="markdown-table">{children}</table>,
                                thead: ({ children }) => <thead className="markdown-thead">{children}</thead>,
                                tbody: ({ children }) => <tbody className="markdown-tbody">{children}</tbody>,
                                tr: ({ children }) => <tr className="markdown-tr">{children}</tr>,
                                th: ({ children }) => <th className="markdown-th">{children}</th>,
                                td: ({ children }) => <td className="markdown-td">{children}</td>,
                              }}
                            >
                              {message.content}
                            </ReactMarkdown>
                            {isLoading && message.role === 'assistant' && (
                              <span className="typing-indicator">
                                <span></span>
                                <span></span>
                                <span></span>
                              </span>
                            )}
                          </div>
                        ) : (
                          isLoading ? (
                            <div className="thinking-indicator">
                              <Text style={{ color: '#b3b3b3' }}>Thinking...</Text>
                              <div className="typing-indicator">
                                <span></span>
                                <span></span>
                                <span></span>
                              </div>
                            </div>
                          ) : ''
                        )
                      )}
                    </div>
                  </Card>
                  {message.role === 'user' && (
                    <Avatar size={36} icon={<UserOutlined />} style={{
                      background: 'linear-gradient(135deg, #4285f4, #1a73e8)',
                      border: '2px solid rgba(255, 255, 255, 0.1)'
                    }} />
                  )}
                </div>
              ))}
            </Space>
          )}
          <div ref={messagesEndRef} />
        </div>

        {/* Input */}
        <Card style={{
          background: 'rgba(26, 26, 26, 0.95)',
          border: '1px solid rgba(255, 255, 255, 0.1)',
          borderRadius: '24px',
          backdropFilter: 'blur(20px)'
        }}>
          <div style={{ display: 'flex', gap: '12px', alignItems: 'flex-end' }}>
            <TextArea
              value={inputValue}
              onChange={(e) => setInputValue(e.target.value)}
              onKeyPress={handleKeyPress}
              placeholder="Type your message here..."
              autoSize={{ minRows: 1, maxRows: 4 }}
              disabled={isLoading}
              style={{
                flex: 1,
                background: 'transparent',
                border: 'none',
                color: 'white',
                resize: 'none'
              }}
            />
            <Button
              type="primary"
              icon={isLoading ? <Spin size="small" /> : <SendOutlined />}
              onClick={handleSendMessage}
              disabled={!inputValue.trim() || isLoading}
              style={{
                background: 'linear-gradient(135deg, #4285f4, #34a853)',
                border: 'none',
                borderRadius: '20px',
                width: '40px',
                height: '40px'
              }}
            />
          </div>
        </Card>
      </div>
    </div>
  );
};

export default ChatApp;
