/* 增强聊天应用样式 */
.enhanced-chat-container {
  height: 100vh;
  width: 100vw;
  position: relative;
  overflow: hidden;
}

/* 动态背景 */
.chat-background {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, #0f0f0f 0%, #1a1a1a 50%, #0f0f0f 100%);
  z-index: 0;
}

.chat-background::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: 
    radial-gradient(circle at 20% 80%, rgba(66, 133, 244, 0.1) 0%, transparent 50%),
    radial-gradient(circle at 80% 20%, rgba(234, 67, 53, 0.1) 0%, transparent 50%),
    radial-gradient(circle at 40% 40%, rgba(52, 168, 83, 0.1) 0%, transparent 50%);
  animation: gradientShift 20s ease-in-out infinite;
  pointer-events: none;
}

/* 桌面端布局 */
.desktop-layout {
  display: flex;
  height: 100vh;
  position: relative;
  z-index: 1;
}

.desktop-sidebar {
  width: 300px;
  background: rgba(26, 26, 26, 0.95);
  backdrop-filter: blur(20px);
  border-right: 1px solid rgba(255, 255, 255, 0.1);
  display: flex;
  flex-direction: column;
}

.main-chat-area {
  flex: 1;
  display: flex;
  flex-direction: column;
  padding: 20px;
  gap: 20px;
}

/* 移动端布局 */
.mobile-layout {
  display: flex;
  flex-direction: column;
  height: 100vh;
  position: relative;
  z-index: 1;
}

/* 侧边栏样式 */
.conversation-sidebar {
  height: 100%;
  display: flex;
  flex-direction: column;
  padding: 20px;
  gap: 16px;
}

.sidebar-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
}

.sidebar-title {
  display: flex;
  align-items: center;
  gap: 8px;
}

.sidebar-icon {
  color: #4285f4;
  font-size: 18px;
}

.sidebar-title-text {
  color: #ffffff !important;
  font-size: 16px;
  font-weight: 600;
}

.sidebar-close-btn {
  color: #b3b3b3 !important;
  border: none !important;
  background: transparent !important;
}

.sidebar-close-btn:hover {
  color: #ffffff !important;
  background: rgba(255, 255, 255, 0.1) !important;
}

/* 新建对话按钮 */
.new-conversation-btn {
  background: linear-gradient(135deg, #4285f4, #34a853) !important;
  border: none !important;
  border-radius: 12px !important;
  height: 44px !important;
  font-weight: 600 !important;
  box-shadow: 0 4px 12px rgba(66, 133, 244, 0.3) !important;
  transition: all 0.3s ease !important;
}

.new-conversation-btn:hover {
  transform: translateY(-1px) !important;
  box-shadow: 0 6px 16px rgba(66, 133, 244, 0.4) !important;
}

/* 对话列表 */
.conversation-list {
  flex: 1;
  overflow-y: auto;
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.conversation-item {
  padding: 16px;
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 12px;
  cursor: pointer;
  transition: all 0.3s ease;
  backdrop-filter: blur(10px);
}

.conversation-item:hover {
  background: rgba(255, 255, 255, 0.1);
  border-color: rgba(66, 133, 244, 0.3);
  transform: translateY(-1px);
}

.conversation-item.active {
  background: rgba(66, 133, 244, 0.2);
  border-color: rgba(66, 133, 244, 0.5);
  box-shadow: 0 4px 12px rgba(66, 133, 244, 0.2);
}

.conversation-content {
  display: flex;
  flex-direction: column;
  gap: 6px;
}

.conversation-title {
  color: #ffffff;
  font-weight: 600;
  font-size: 14px;
  line-height: 1.4;
}

.conversation-preview {
  color: #b3b3b3;
  font-size: 12px;
  line-height: 1.3;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.conversation-meta {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: 4px;
}

.conversation-time,
.conversation-count {
  color: #666666;
  font-size: 11px;
}

.empty-conversations {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100px;
}

.empty-text {
  color: #666666 !important;
  font-size: 14px;
}

/* 聊天头部 */
.chat-header {
  background: rgba(26, 26, 26, 0.95) !important;
  border: 1px solid rgba(255, 255, 255, 0.1) !important;
  border-radius: 12px !important;
  backdrop-filter: blur(20px);
}

.header-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.header-info {
  display: flex;
  align-items: center;
  gap: 12px;
}

.logo-avatar {
  background: linear-gradient(135deg, #4285f4, #34a853) !important;
  border: 2px solid rgba(255, 255, 255, 0.2) !important;
}

.header-text {
  display: flex;
  flex-direction: column;
}

.header-title {
  color: #ffffff !important;
  margin: 0 !important;
  font-weight: 600;
  font-size: 20px;
}

.header-subtitle {
  color: #b3b3b3 !important;
  font-size: 12px;
}

.header-actions {
  display: flex;
  gap: 12px;
}

.header-new-btn {
  background: linear-gradient(135deg, #4285f4, #34a853) !important;
  border: none !important;
  border-radius: 8px !important;
  height: 36px !important;
  font-weight: 500 !important;
}

/* 消息区域 */
.messages-container {
  flex: 1;
  overflow-y: auto;
  padding: 20px;
  display: flex;
  flex-direction: column;
}

.welcome-screen {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100%;
  text-align: center;
  animation: messageSlideIn 0.5s ease-out;
}

.welcome-avatar {
  background: linear-gradient(135deg, #4285f4, #34a853) !important;
  margin-bottom: 24px !important;
  border: 3px solid rgba(255, 255, 255, 0.1) !important;
  box-shadow: 0 8px 32px rgba(66, 133, 244, 0.3);
}

.welcome-title {
  color: #ffffff !important;
  margin-bottom: 16px !important;
  font-weight: 600;
}

.welcome-description {
  color: #b3b3b3 !important;
  font-size: 16px;
  line-height: 1.6;
}

/* 消息样式 */
.message-wrapper {
  display: flex;
  gap: 12px;
  animation: messageSlideIn 0.3s ease-out;
}

.user-message {
  justify-content: flex-end;
}

.assistant-message {
  justify-content: flex-start;
}

.user-message .message-content {
  flex-direction: row-reverse;
}

.message-card {
  max-width: 80%;
  border-radius: 18px !important;
  backdrop-filter: blur(10px);
  transition: all 0.3s ease;
}

.user-card {
  background: linear-gradient(135deg, rgba(66, 133, 244, 0.2), rgba(26, 115, 232, 0.2)) !important;
  border: 1px solid rgba(66, 133, 244, 0.3) !important;
}

.assistant-card {
  background: rgba(255, 255, 255, 0.05) !important;
  border: 1px solid rgba(255, 255, 255, 0.1) !important;
}

.message-card:hover {
  transform: translateY(-1px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.2);
}

.message-content {
  color: #ffffff;
  line-height: 1.6;
}

.user-text {
  color: #ffffff !important;
}

.assistant-avatar {
  background: linear-gradient(135deg, #34a853, #137333) !important;
  border: 2px solid rgba(255, 255, 255, 0.1) !important;
  margin-top: 4px;
}

.user-avatar {
  background: linear-gradient(135deg, #4285f4, #1a73e8) !important;
  border: 2px solid rgba(255, 255, 255, 0.1) !important;
  margin-top: 4px;
}

/* 输入区域 */
.input-container {
  background: rgba(26, 26, 26, 0.95) !important;
  border: 1px solid rgba(255, 255, 255, 0.1) !important;
  border-radius: 24px !important;
  backdrop-filter: blur(20px);
}

.input-wrapper {
  display: flex;
  gap: 12px;
  align-items: flex-end;
}

.message-input {
  flex: 1;
  background: transparent !important;
  border: none !important;
  color: #ffffff !important;
  resize: none;
}

.message-input::placeholder {
  color: #666666;
}

.send-button {
  background: linear-gradient(135deg, #4285f4, #34a853) !important;
  border: none !important;
  border-radius: 20px !important;
  width: 40px !important;
  height: 40px !important;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
  transition: all 0.3s ease !important;
  box-shadow: 0 4px 12px rgba(66, 133, 244, 0.3) !important;
}

.send-button:hover {
  transform: translateY(-1px) !important;
  box-shadow: 0 6px 16px rgba(66, 133, 244, 0.4) !important;
}

/* 移动端样式 */
.mobile-header {
  background: rgba(26, 26, 26, 0.95) !important;
  border: none !important;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1) !important;
  border-radius: 0 !important;
  backdrop-filter: blur(20px);
}

.mobile-header-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 0;
}

.mobile-menu-btn {
  color: #ffffff !important;
  border: none !important;
  background: transparent !important;
}

.mobile-header-info {
  flex: 1;
  text-align: center;
}

.mobile-title {
  color: #ffffff !important;
  margin: 0 !important;
  font-size: 18px;
}

.mobile-new-btn {
  background: linear-gradient(135deg, #4285f4, #34a853) !important;
  border: none !important;
  border-radius: 8px !important;
  height: 32px !important;
  width: 32px !important;
  padding: 0 !important;
}

.mobile-messages {
  flex: 1;
  overflow-y: auto;
  padding: 16px;
}

.mobile-input {
  background: rgba(26, 26, 26, 0.95) !important;
  border: none !important;
  border-top: 1px solid rgba(255, 255, 255, 0.1) !important;
  border-radius: 0 !important;
  backdrop-filter: blur(20px);
}

.mobile-drawer .ant-drawer-content {
  background: rgba(26, 26, 26, 0.95) !important;
  backdrop-filter: blur(20px);
}

.mobile-drawer .ant-drawer-body {
  padding: 0 !important;
}

/* 思考指示器 */
.thinking-indicator {
  display: flex;
  align-items: center;
  gap: 8px;
}

.thinking-text {
  color: #b3b3b3 !important;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .desktop-layout {
    display: none;
  }
  
  .mobile-layout {
    display: flex;
  }
  
  .message-card {
    max-width: 90%;
  }
  
  .welcome-avatar {
    width: 60px !important;
    height: 60px !important;
  }
  
  .welcome-title {
    font-size: 20px !important;
  }
  
  .welcome-description {
    font-size: 14px !important;
  }
}

@media (min-width: 769px) {
  .mobile-layout {
    display: none;
  }
  
  .desktop-layout {
    display: flex;
  }
}
