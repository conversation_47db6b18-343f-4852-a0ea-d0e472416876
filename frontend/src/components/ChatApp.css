/* Markdown 内容样式 */
.markdown-content {
  color: #ffffff;
  font-family: 'Google Sans', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  line-height: 1.6;
}

/* 段落样式 */
.markdown-paragraph {
  margin-bottom: 12px;
  line-height: 1.6;
  color: #ffffff;
}

.markdown-paragraph:last-child {
  margin-bottom: 0;
}

/* 标题样式 */
.markdown-heading {
  color: #ffffff;
  margin: 16px 0 8px 0;
  font-weight: 600;
  line-height: 1.3;
}

.markdown-heading:first-child {
  margin-top: 0;
}

/* 列表样式 */
.markdown-list {
  margin: 8px 0;
  padding-left: 20px;
  color: #ffffff;
}

.markdown-list-item {
  margin: 4px 0;
  color: #ffffff;
}

/* 引用样式 */
.markdown-blockquote {
  border-left: 4px solid rgba(66, 133, 244, 0.5);
  margin: 12px 0;
  padding: 8px 16px;
  background: rgba(255, 255, 255, 0.05);
  border-radius: 8px;
  font-style: italic;
  color: #e0e0e0;
}

/* 强调样式 */
.markdown-strong {
  font-weight: 700;
  color: #ffffff;
}

.markdown-em {
  font-style: italic;
  color: #e0e0e0;
}

/* 分割线样式 */
.markdown-hr {
  border: none;
  height: 1px;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
  margin: 20px 0;
}

/* 内联代码样式 */
.inline-code {
  background: rgba(255, 255, 255, 0.1) !important;
  color: #ff6b6b !important;
  padding: 2px 6px !important;
  border-radius: 4px !important;
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace !important;
  font-size: 13px !important;
  border: 1px solid rgba(255, 255, 255, 0.1) !important;
}

/* 代码块包装器 */
.code-block-wrapper {
  position: relative;
  margin: 16px 0;
  border-radius: 12px;
  overflow: hidden;
  border: 1px solid rgba(255, 255, 255, 0.1);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
  background: rgba(0, 0, 0, 0.4);
}

/* 语言标签 */
.code-language-label {
  position: absolute;
  top: 8px;
  right: 12px;
  background: rgba(66, 133, 244, 0.8);
  color: #ffffff;
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 11px;
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  text-transform: uppercase;
  font-weight: 600;
  z-index: 10;
  backdrop-filter: blur(4px);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

/* 代码块样式 */
.code-block {
  margin: 0 !important;
  border-radius: 0 !important;
  border: none !important;
  box-shadow: none !important;
  background: transparent !important;
}

.code-block pre {
  margin: 0 !important;
  background: transparent !important;
  padding: 16px !important;
  padding-top: 40px !important; /* 为语言标签留出空间 */
  font-size: 13px !important;
  line-height: 1.5 !important;
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace !important;
  overflow-x: auto;
}

/* 表格样式 */
.markdown-table {
  width: 100%;
  border-collapse: collapse;
  margin: 16px 0;
  background: rgba(255, 255, 255, 0.05);
  border-radius: 8px;
  overflow: hidden;
}

.markdown-thead {
  background: rgba(66, 133, 244, 0.2);
}

.markdown-th,
.markdown-td {
  padding: 12px;
  text-align: left;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
  color: #ffffff;
}

.markdown-th {
  font-weight: 600;
  background: rgba(255, 255, 255, 0.05);
}

.markdown-tr:hover {
  background: rgba(255, 255, 255, 0.05);
}

/* 思考指示器 */
.thinking-indicator {
  display: flex;
  align-items: center;
  gap: 8px;
}

/* 打字指示器 */
.typing-indicator {
  display: inline-flex;
  align-items: center;
  margin-left: 8px;
}

.typing-indicator span {
  height: 4px;
  width: 4px;
  background: rgba(66, 133, 244, 0.7);
  border-radius: 50%;
  display: inline-block;
  margin: 0 1px;
  animation: typing 1.4s infinite ease-in-out;
}

.typing-indicator span:nth-child(1) {
  animation-delay: -0.32s;
}

.typing-indicator span:nth-child(2) {
  animation-delay: -0.16s;
}

@keyframes typing {
  0%, 80%, 100% {
    transform: scale(0.8);
    opacity: 0.5;
  }
  40% {
    transform: scale(1);
    opacity: 1;
  }
}

/* 响应式设计 */
@media (max-width: 768px) {
  .markdown-content {
    font-size: 14px;
  }
  
  .code-block pre {
    padding: 12px !important;
    font-size: 12px !important;
  }
  
  .markdown-table {
    font-size: 12px;
  }
  
  .markdown-th,
  .markdown-td {
    padding: 8px;
  }
}

/* 特殊样式增强 */
.markdown-content h1 {
  font-size: 1.8em;
  color: #4285f4;
  border-bottom: 2px solid rgba(66, 133, 244, 0.3);
  padding-bottom: 8px;
}

.markdown-content h2 {
  font-size: 1.5em;
  color: #34a853;
}

.markdown-content h3 {
  font-size: 1.3em;
  color: #fbbc04;
}

.markdown-content h4 {
  font-size: 1.1em;
  color: #ea4335;
}

/* 链接样式 */
.markdown-link {
  color: #4285f4 !important;
  text-decoration: none !important;
  border-bottom: 1px solid transparent !important;
  transition: all 0.3s ease !important;
  position: relative !important;
}

.markdown-link:hover {
  border-bottom-color: #4285f4 !important;
  color: #5a9df7 !important;
  text-decoration: none !important;
}

.markdown-link:visited {
  color: #8e7cc3 !important;
}

/* 外部链接图标 */
.markdown-link::after {
  content: "↗";
  font-size: 0.8em;
  margin-left: 4px;
  opacity: 0.7;
}

/* 图片样式 */
.markdown-image {
  max-width: 100%;
  height: auto;
  border-radius: 8px;
  margin: 12px 0;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
  border: 1px solid rgba(255, 255, 255, 0.1);
  transition: transform 0.3s ease;
}

.markdown-image:hover {
  transform: scale(1.02);
}

/* 数字列表特殊样式 */
.markdown-content ol {
  counter-reset: item;
}

.markdown-content ol li {
  display: block;
  margin-bottom: 0.5em;
  position: relative;
}

.markdown-content ol li:before {
  content: counter(item) ".";
  counter-increment: item;
  font-weight: bold;
  color: #4285f4;
  margin-right: 8px;
}

/* 修复代码块内的文本选择 */
.code-block * {
  user-select: text;
}

/* 代码块复制按钮区域 */
.code-block {
  position: relative;
}

/* 修复内联代码在不同元素中的显示 */
.markdown-content p code,
.markdown-content li code,
.markdown-content td code,
.markdown-content th code {
  background: rgba(255, 255, 255, 0.15) !important;
  color: #ff9999 !important;
  padding: 2px 4px !important;
  border-radius: 3px !important;
  font-size: 0.9em !important;
}

/* 修复列表嵌套 */
.markdown-content ul ul,
.markdown-content ol ol,
.markdown-content ul ol,
.markdown-content ol ul {
  margin: 4px 0;
  padding-left: 20px;
}

/* 修复表格在小屏幕上的显示 */
.markdown-table {
  display: block;
  overflow-x: auto;
  white-space: nowrap;
}

/* 修复引用块内的其他元素 */
.markdown-blockquote p {
  margin-bottom: 8px;
}

.markdown-blockquote p:last-child {
  margin-bottom: 0;
}

/* 修复标题的间距 */
.markdown-content h1 + p,
.markdown-content h2 + p,
.markdown-content h3 + p,
.markdown-content h4 + p,
.markdown-content h5 + p,
.markdown-content h6 + p {
  margin-top: 8px;
}

/* 修复代码块语言标签 */
.code-block::before {
  content: attr(data-language);
  position: absolute;
  top: 8px;
  right: 12px;
  background: rgba(255, 255, 255, 0.1);
  color: #b3b3b3;
  padding: 2px 8px;
  border-radius: 4px;
  font-size: 11px;
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  text-transform: uppercase;
  z-index: 1;
}

/* 修复长文本的换行 */
.markdown-content {
  word-wrap: break-word;
  overflow-wrap: break-word;
}

/* 修复代码块的滚动 */
.code-block pre {
  overflow-x: auto;
  max-width: 100%;
}

/* 修复移动端代码块 */
@media (max-width: 768px) {
  .code-block-wrapper {
    margin: 12px 0;
    border-radius: 8px;
  }

  .code-language-label {
    top: 6px;
    right: 8px;
    padding: 2px 6px;
    font-size: 10px;
  }

  .code-block pre {
    font-size: 11px !important;
    padding: 12px !important;
    padding-top: 32px !important;
    line-height: 1.4 !important;
  }

  .inline-code {
    font-size: 11px !important;
    padding: 1px 4px !important;
  }

  .markdown-table {
    font-size: 11px;
  }

  .markdown-content h1 {
    font-size: 1.5em;
  }

  .markdown-content h2 {
    font-size: 1.3em;
  }

  .markdown-content h3 {
    font-size: 1.2em;
  }
}
